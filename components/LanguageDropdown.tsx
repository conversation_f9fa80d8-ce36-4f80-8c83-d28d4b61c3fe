import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet, Modal, Pressable } from 'react-native';
import { useTranslation } from 'react-i18next';
import AntDesign from '@expo/vector-icons/AntDesign';
import Text6 from '@/components/CustomText';
import { SUPPORTED_LANGUAGES, UILanguage, changeLanguage, getCurrentLanguage } from '@/utils/locale';

interface LanguageDropdownProps {
  iconSize?: number;
  iconColor?: string;
}

const LanguageDropdown: React.FC<LanguageDropdownProps> = ({ 
  iconSize = 24, 
  iconColor = '#333' 
}) => {
  const { t } = useTranslation();
  const [isVisible, setIsVisible] = useState(false);
  const currentLanguage = getCurrentLanguage();

  const handleLanguageChange = async (language: UILanguage) => {
    try {
      await changeLanguage(language);
      setIsVisible(false);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  const getLanguageDisplayName = (language: UILanguage) => {
    return t(`languageOptions.${language === 'ko' ? 'korean' : 'english'}`);
  };

  return (
    <>
      <TouchableOpacity
        onPress={() => setIsVisible(true)}
        style={styles.iconButton}
        activeOpacity={0.7}
      >
        <AntDesign name="earth" size={iconSize} color={iconColor} />
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsVisible(false)}
      >
        <Pressable 
          style={styles.modalOverlay} 
          onPress={() => setIsVisible(false)}
        >
          <View style={styles.dropdown}>
            <Text6 weight="medium" style={styles.dropdownTitle}>
              {t('languageOptions.title')}
            </Text6>
            
            {SUPPORTED_LANGUAGES.map((language) => (
              <TouchableOpacity
                key={language}
                style={[
                  styles.languageOption,
                  currentLanguage === language && styles.selectedOption
                ]}
                onPress={() => handleLanguageChange(language)}
                activeOpacity={0.7}
              >
                <Text6 
                  weight={currentLanguage === language ? 'medium' : 'regular'}
                  style={[
                    styles.languageText,
                    currentLanguage === language && styles.selectedText
                  ]}
                >
                  {getLanguageDisplayName(language)}
                </Text6>
                {currentLanguage === language && (
                  <AntDesign name="check" size={16} color="#FB9E3A" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </Pressable>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  iconButton: {
    padding: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdown: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    minWidth: 200,
    maxWidth: 280,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  dropdownTitle: {
    fontSize: 18,
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginVertical: 2,
  },
  selectedOption: {
    backgroundColor: '#FFF5E6',
  },
  languageText: {
    fontSize: 16,
    color: '#333',
  },
  selectedText: {
    color: '#FB9E3A',
  },
});

export default LanguageDropdown;
