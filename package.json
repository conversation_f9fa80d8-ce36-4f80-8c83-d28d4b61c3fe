{"name": "leftovers", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"prebuild": "expo prebuild", "android": "EXPO_NO_TELEMETRY=1 expo run:android", "ios": "EXPO_NO_TELEMETRY=1 expo run:ios", "lint": "expo lint", "build:prod": "eas build --profile production --local", "build:dev": "eas build --profile development --local"}, "dependencies": {"@expo-google-fonts/geist-mono": "^0.4.0", "@expo-google-fonts/gowun-batang": "^0.4.0", "@expo-google-fonts/noto-sans-kr": "^0.4.1", "@expo-google-fonts/outfit": "^0.4.2", "@expo-google-fonts/poppins": "^0.4.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "expo": "~53.0.12", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.1", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-tracking-transparency": "~5.2.4", "expo-updates": "~0.28.15", "expo-web-browser": "~14.2.0", "i18next": "^25.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.5.3", "react-native": "0.79.4", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-google-mobile-ads": "^15.4.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "ts-node": "^10.9.2", "typescript": "~5.8.3"}, "private": true, "expo": {"install": {"exclude": ["i18next"]}, "doctor": {"reactNativeDirectoryCheck": {"exclude": ["i18next"]}}}, "jest": {"preset": "jest-expo"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}